using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Script.Serialization;

namespace LinCom
{
    /// <summary>
    /// Handler pour l'upload de fichiers dans la messagerie
    /// </summary>
    public class FileUploadHandler : IHttpHandler
    {
        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "application/json";
            context.Response.Headers.Add("Access-Control-Allow-Origin", "*");

            try
            {
                // Log pour debug
                System.Diagnostics.Debug.WriteLine("FileUploadHandler: Début du traitement");
                System.Diagnostics.Debug.WriteLine($"Nombre de fichiers: {context.Request.Files.Count}");
                if (context.Request.Files.Count > 0)
                {
                    var file = context.Request.Files[0];
                    
                    if (file != null && file.ContentLength > 0)
                    {
                        // Vérifications de sécurité
                        if (!IsValidFile(file))
                        {
                            WriteErrorResponse(context, "Type de fichier non autorisé");
                            return;
                        }

                        if (file.ContentLength > 10 * 1024 * 1024) // 10MB max
                        {
                            WriteErrorResponse(context, "Fichier trop volumineux (max 10MB)");
                            return;
                        }

                        // Créer le dossier uploads s'il n'existe pas
                        string uploadsPath = context.Server.MapPath("~/uploads/");
                        if (!Directory.Exists(uploadsPath))
                        {
                            Directory.CreateDirectory(uploadsPath);
                        }

                        // Générer un nom de fichier unique
                        string fileName = GenerateUniqueFileName(file.FileName);
                        string filePath = Path.Combine(uploadsPath, fileName);

                        // Sauvegarder le fichier
                        file.SaveAs(filePath);

                        // Retourner la réponse de succès
                        var response = new
                        {
                            success = true,
                            fileName = fileName,
                            originalName = file.FileName,
                            fileSize = file.ContentLength,
                            fileUrl = "~/uploads/" + fileName,
                            message = "Fichier uploadé avec succès"
                        };

                        WriteJsonResponse(context, response);
                    }
                    else
                    {
                        WriteErrorResponse(context, "Aucun fichier sélectionné");
                    }
                }
                else
                {
                    WriteErrorResponse(context, "Aucun fichier reçu");
                }
            }
            catch (Exception ex)
            {
                WriteErrorResponse(context, "Erreur lors de l'upload: " + ex.Message);
            }
        }

        private bool IsValidFile(HttpPostedFile file)
        {
            // Extensions autorisées
            string[] allowedExtensions = { 
                ".jpg", ".jpeg", ".png", ".gif", ".bmp",  // Images
                ".pdf",                                    // PDF
                ".doc", ".docx",                          // Word
                ".xls", ".xlsx",                          // Excel
                ".ppt", ".pptx",                          // PowerPoint
                ".txt", ".rtf",                           // Texte
                ".zip", ".rar",                           // Archives
                ".mp3", ".wav",                           // Audio
                ".mp4", ".avi", ".mov"                    // Vidéo
            };

            string extension = Path.GetExtension(file.FileName).ToLower();
            return allowedExtensions.Contains(extension);
        }

        private string GenerateUniqueFileName(string originalFileName)
        {
            string extension = Path.GetExtension(originalFileName);
            string nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            
            // Nettoyer le nom de fichier
            nameWithoutExtension = CleanFileName(nameWithoutExtension);
            
            // Ajouter un timestamp pour l'unicité
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string uniqueId = Guid.NewGuid().ToString("N").Substring(0, 8);
            
            return $"{nameWithoutExtension}_{timestamp}_{uniqueId}{extension}";
        }

        private string CleanFileName(string fileName)
        {
            // Remplacer les caractères non autorisés
            char[] invalidChars = Path.GetInvalidFileNameChars();
            foreach (char c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }
            
            // Remplacer les espaces par des underscores
            fileName = fileName.Replace(' ', '_');
            
            // Limiter la longueur
            if (fileName.Length > 50)
            {
                fileName = fileName.Substring(0, 50);
            }
            
            return fileName;
        }

        private void WriteJsonResponse(HttpContext context, object data)
        {
            JavaScriptSerializer serializer = new JavaScriptSerializer();
            string json = serializer.Serialize(data);
            context.Response.Write(json);
        }

        private void WriteErrorResponse(HttpContext context, string errorMessage)
        {
            var errorResponse = new
            {
                success = false,
                message = errorMessage
            };
            WriteJsonResponse(context, errorResponse);
        }

        public bool IsReusable
        {
            get { return false; }
        }
    }
}
