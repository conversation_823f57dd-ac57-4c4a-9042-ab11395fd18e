﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class MessageImp : IMessage
    {
        int msg;
        private Message message = new Message();
        private MessageStatu mesast= new MessageStatu();

        public void AfficherDetails(long messageId, Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageId);
                if (m != null)
                {
                    messageClass.MessageId = m.MessageId;
                    messageClass.ConversationId = m.ConversationId;
                    messageClass.SenderId = m.SenderId;
                    messageClass.Contenu = m.Contenu;
                    messageClass.AttachmentUrl = m.AttachmentUrl;
                    messageClass.DateEnvoi = m.DateEnvoi;
                    messageClass.name = m.name;

    }
            }
        }

      

        public void ChargerMessages(Repeater rpt, long conversationId, int nombreMessages)
        {
            using (Connection con = new Connection())
            {
                var messages = from m in con.Messages
                               join mb in con.Membres on m.SenderId equals mb.MembreId
                               where m.ConversationId == conversationId
                               orderby m.DateEnvoi descending
                               select new
                               {
                                   id = m.MessageId,
                                   Contenu = m.Contenu ?? "",
                                   Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                   Photomembre = mb.PhotoProfil ?? "",
                                   DateEnvoi = m.DateEnvoi,
                                   name = m.name ?? "",
                                   AttachmentUrl = m.AttachmentUrl ?? ""

                               };

                rpt.DataSource = messages.Take(nombreMessages).ToList();
                rpt.DataBind();
            }
        }

        public int CompterNonLus(long membreId)
        {
            using (Connection con = new Connection())
            {
                return con.Messages
                    .Count(m => m.SenderId != membreId &&
                               con.ParticipantConversations.Any(p =>
                                   p.ConversationId == m.ConversationId &&
                                   p.MembreId == membreId));
            }
        }

        public int Envoyer(Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                message.ConversationId = messageClass.ConversationId;
                message.SenderId = messageClass.SenderId;
                message.Contenu = messageClass.Contenu;
                message.DateEnvoi = DateTime.Now;
                message.name = messageClass.name;
                message.AttachmentUrl=messageClass.AttachmentUrl;

                try
                {
                    con.Messages.Add(message);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }

        public int EnvoyerMessageStatus(MessageStatus_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                mesast.MessageId = messageClass.MessageId;
                mesast.UserId = messageClass.UserId;
                mesast.IsRead = messageClass.IsRead;
                mesast.ReadAt = DateTime.Now;
              
                try
                {
                    con.MessageStatus.Add(mesast);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }



        public int Modifier(Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageClass.MessageId);
                if (m != null)
                {
                    m.Contenu = messageClass.Contenu;
                    m.name = messageClass.name;

                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public int Supprimer(long messageId)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageId);
                if (m != null)
                {
                    con.Messages.Remove(m);
                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }


        //Methodes pour Messages Statut

        public void AfficherDetailsMessageStatut(long statusId, MessageStatus_Class statusClass)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessagestatusID == statusId);
                if (status != null)
                {
                    statusClass.MessagestatusID = status.MessagestatusID;
                    statusClass.MessageId = status.MessageId;
                    statusClass.UserId = status.UserId;
                    statusClass.IsRead = status.IsRead;
                    statusClass.ReadAt = status.ReadAt;
                }
            }
        }

        public int MarquerCommeLu(long messageId, long userId)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessageId == messageId && x.UserId == userId);
                if (status != null)
                {
                    status.IsRead = 1;
                    status.ReadAt = DateTime.Now;
                    return con.SaveChanges();
                }
                return 0;
            }
        }

        public int AjouterMessageEtStatusPourTous(long conversationId, long senderId, string contenu, string attachmentUrl = null)
        {
            using (var con = new Connection())
            {
                using (var transaction = con.Database.BeginTransaction())
                {
                    try
                    {
                        // 1. Création et ajout du message
                        var message = new Message
                        {
                            ConversationId = conversationId,
                            SenderId = senderId,
                            Contenu = contenu,
                            AttachmentUrl = attachmentUrl,
                            DateEnvoi = DateTime.Now,
                            name = "Nom ou pseudo de l'expéditeur" // adapte selon ton contexte
                        };

                        con.Messages.Add(message);
                        con.SaveChanges(); // Génère MessageId

                        // 2. Récupérer tous les participants de la conversation
                        var participants = con.ParticipantConversations
                                              .Where(pc => pc.ConversationId == conversationId)
                                              .Select(pc => pc.MembreId)
                                              .ToList();

                        // 3. Créer les MessageStatus pour tous
                        foreach (var membreId in participants)
                        {
                            var status = new MessageStatu
                            {
                                MessageId = message.MessageId,
                                UserId = (long)membreId,
                                IsRead = (membreId == senderId) ? 1 : 0,
                                ReadAt = (membreId == senderId) ? (DateTime?)DateTime.Now : null
                            };
                            con.MessageStatus.Add(status);
                        }

                        con.SaveChanges();
                        transaction.Commit();

                        return 1; // succès
                    }
                    catch
                    {
                        transaction.Rollback();
                        return 0; // échec
                    }
                }
            }
        }


        public int SupprimerMessageStatut(long messageStatusId)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessagestatusID == messageStatusId);
                if (status != null)
                {
                    con.MessageStatus.Remove(status);
                    return con.SaveChanges();
                }
                return 0;
            }
        }

        public void RechercherMessages(Repeater rpt, long membreId, string termeRecherche)
        {
            using (Connection con = new Connection())
            {
                // Récupérer les conversations de l'utilisateur
                var conversations = (from p in con.ParticipantConversations
                                    where p.MembreId == membreId
                                    select p.ConversationId).ToList();

                var messages = from m in con.Messages
                               join mb in con.Membres on m.SenderId equals mb.MembreId
                               where conversations.Contains(m.ConversationId) &&
                                     (m.Contenu.Contains(termeRecherche) ||
                                      mb.Nom.Contains(termeRecherche) ||
                                      mb.Prenom.Contains(termeRecherche))
                               orderby m.DateEnvoi descending
                               select new
                               {
                                   id = m.MessageId,
                                   Contenu = m.Contenu ?? "",
                                   Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                   Photomembre = mb.PhotoProfil ?? "",
                                   DateEnvoi = m.DateEnvoi,
                                   name = m.name ?? "",
                                   AttachmentUrl = m.AttachmentUrl ?? ""
                               };

                rpt.DataSource = messages.Take(50).ToList();
                rpt.DataBind();
            }
        }

        public void MarquerConversationCommeLue(long conversationId, long membreId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    // Récupérer tous les messages de la conversation
                    var messagesConversation = con.Messages
                        .Where(m => m.ConversationId == conversationId)
                        .Select(m => m.MessageId)
                        .ToList();

                    // Marquer tous les statuts de ces messages comme lus pour cet utilisateur
                    var messagesNonLus = con.MessageStatus
                        .Where(ms => messagesConversation.Contains(ms.MessageId) &&
                                    ms.UserId == membreId &&
                                    ms.IsRead == 0)
                        .ToList();

                    foreach (var messageStatus in messagesNonLus)
                    {
                        messageStatus.IsRead = 1;
                        messageStatus.ReadAt = DateTime.Now;
                    }

                    con.SaveChanges();
                }
                catch (Exception ex)
                {
                    // Log l'erreur si nécessaire
                    System.Diagnostics.Debug.WriteLine("Erreur lors du marquage comme lu: " + ex.Message);
                }
            }
        }

    }
}