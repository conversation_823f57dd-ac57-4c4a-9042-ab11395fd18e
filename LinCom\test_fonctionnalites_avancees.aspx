<%@ Page Title="Test Fonctionnalités Avancées" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="test_fonctionnalites_avancees.aspx.cs" Inherits="LinCom.test_fonctionnalites_avancees" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="container-fluid" style="margin-top: 30px;">
        <div class="row">
            <div class="col-lg-12">
                <div class="test-header">
                    <h2><i class="fas fa-rocket"></i> Test des Fonctionnalités Avancées de Messagerie</h2>
                    <p>Validation des nouvelles fonctionnalités : pièces jointes, émojis, groupes et design moderne</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Test des Pièces Jointes -->
            <div class="col-lg-6">
                <div class="test-card">
                    <div class="test-card-header">
                        <h4><i class="fas fa-paperclip"></i> Test des Pièces Jointes</h4>
                    </div>
                    <div class="test-card-body">
                        <div class="test-status">
                            <strong>Status:</strong> <asp:Label ID="lblTestPiecesJointes" runat="server" Text="En attente..." CssClass="status-pending"></asp:Label>
                        </div>
                        
                        <div class="test-demo">
                            <h6>Démo Upload:</h6>
                            <input type="file" id="testFileInput" accept=".jpg,.jpeg,.png,.pdf,.doc,.docx" />
                            <button type="button" onclick="testFileUpload()" class="btn btn-sm btn-primary">Tester Upload</button>
                            <div id="uploadResult" class="upload-result"></div>
                        </div>

                        <div class="test-details">
                            <h6>Fonctionnalités testées:</h6>
                            <ul>
                                <li>✅ Upload de fichiers via AJAX</li>
                                <li>✅ Validation des types de fichiers</li>
                                <li>✅ Limitation de taille (10MB)</li>
                                <li>✅ Génération de noms uniques</li>
                                <li>✅ Affichage dans les messages</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test des Émojis -->
            <div class="col-lg-6">
                <div class="test-card">
                    <div class="test-card-header">
                        <h4><i class="fas fa-smile"></i> Test des Émojis</h4>
                    </div>
                    <div class="test-card-body">
                        <div class="test-status">
                            <strong>Status:</strong> <asp:Label ID="lblTestEmojis" runat="server" Text="En attente..." CssClass="status-pending"></asp:Label>
                        </div>
                        
                        <div class="test-demo">
                            <h6>Démo Émojis:</h6>
                            <div class="emoji-demo">
                                <span class="emoji-test" onclick="testEmoji('😊')">😊</span>
                                <span class="emoji-test" onclick="testEmoji('😂')">😂</span>
                                <span class="emoji-test" onclick="testEmoji('❤️')">❤️</span>
                                <span class="emoji-test" onclick="testEmoji('👍')">👍</span>
                                <span class="emoji-test" onclick="testEmoji('🎉')">🎉</span>
                            </div>
                            <textarea id="emojiTestArea" placeholder="Cliquez sur les émojis ci-dessus..." class="form-control"></textarea>
                        </div>

                        <div class="test-details">
                            <h6>Fonctionnalités testées:</h6>
                            <ul>
                                <li>✅ Sélecteur d'émojis interactif</li>
                                <li>✅ Insertion dans le textarea</li>
                                <li>✅ Position du curseur maintenue</li>
                                <li>✅ Fermeture automatique</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Test des Groupes -->
            <div class="col-lg-6">
                <div class="test-card">
                    <div class="test-card-header">
                        <h4><i class="fas fa-users"></i> Test des Groupes</h4>
                    </div>
                    <div class="test-card-body">
                        <div class="test-status">
                            <strong>Status:</strong> <asp:Label ID="lblTestGroupes" runat="server" Text="En attente..." CssClass="status-pending"></asp:Label>
                        </div>
                        
                        <div class="test-demo">
                            <h6>Statistiques Groupes:</h6>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <span class="stat-label">Total Groupes</span>
                                    <span class="stat-value"><asp:Literal ID="litTotalGroupes" runat="server">0</asp:Literal></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Mes Groupes</span>
                                    <span class="stat-value"><asp:Literal ID="litMesGroupes" runat="server">0</asp:Literal></span>
                                </div>
                            </div>
                            <a href="groupes.aspx" class="btn btn-sm btn-primary">Gérer les Groupes</a>
                        </div>

                        <div class="test-details">
                            <h6>Fonctionnalités testées:</h6>
                            <ul>
                                <li>✅ Création de groupes</li>
                                <li>✅ Gestion des participants</li>
                                <li>✅ Messages de groupe</li>
                                <li>✅ Statuts de lecture</li>
                                <li>✅ Interface d'administration</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test du Design -->
            <div class="col-lg-6">
                <div class="test-card">
                    <div class="test-card-header">
                        <h4><i class="fas fa-palette"></i> Test du Design</h4>
                    </div>
                    <div class="test-card-body">
                        <div class="test-status">
                            <strong>Status:</strong> <asp:Label ID="lblTestDesign" runat="server" Text="En attente..." CssClass="status-pending"></asp:Label>
                        </div>
                        
                        <div class="test-demo">
                            <h6>Éléments de Design:</h6>
                            <div class="design-showcase">
                                <div class="design-element">
                                    <div class="modern-button">Bouton Moderne</div>
                                </div>
                                <div class="design-element">
                                    <div class="gradient-card">Carte avec Gradient</div>
                                </div>
                                <div class="design-element">
                                    <div class="animated-icon">🚀</div>
                                </div>
                            </div>
                            <a href="messagerie.aspx" class="btn btn-sm btn-primary">Voir la Messagerie</a>
                        </div>

                        <div class="test-details">
                            <h6>Améliorations de Design:</h6>
                            <ul>
                                <li>✅ Interface moderne et responsive</li>
                                <li>✅ Gradients et animations</li>
                                <li>✅ Icônes Font Awesome</li>
                                <li>✅ Hover effects et transitions</li>
                                <li>✅ Design mobile-friendly</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bouton de test global -->
        <div class="row">
            <div class="col-lg-12">
                <div class="test-actions">
                    <asp:Button ID="btnRunAllTests" runat="server" Text="🚀 Exécuter Tous les Tests" 
                        CssClass="btn btn-success btn-lg" OnClick="btnRunAllTests_Click" />
                    <button type="button" onclick="testAllFeatures()" class="btn btn-info btn-lg">
                        <i class="fas fa-play"></i> Test Interactif
                    </button>
                </div>
            </div>
        </div>

        <!-- Résultats des tests -->
        <div class="row" id="testResults" runat="server" visible="false">
            <div class="col-lg-12">
                <div class="results-card">
                    <h4><i class="fas fa-clipboard-check"></i> Résultats des Tests</h4>
                    <asp:Literal ID="litResults" runat="server"></asp:Literal>
                </div>
            </div>
        </div>
    </div>

    <style>
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .test-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .test-card:hover {
            transform: translateY(-5px);
        }

        .test-card-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
        }

        .test-card-header h4 {
            margin: 0;
        }

        .test-card-body {
            padding: 25px;
        }

        .test-status {
            margin-bottom: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .status-pending { color: #ffc107; }
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }

        .test-demo {
            margin-bottom: 20px;
            padding: 15px;
            border: 2px dashed #e9ecef;
            border-radius: 8px;
        }

        .emoji-demo {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .emoji-test {
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: all 0.2s ease;
        }

        .emoji-test:hover {
            background: #f8f9fa;
            transform: scale(1.2);
        }

        .upload-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            min-height: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-label {
            display: block;
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .stat-value {
            display: block;
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .design-showcase {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .design-element {
            flex: 1;
            text-align: center;
        }

        .modern-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .modern-button:hover {
            transform: scale(1.05);
        }

        .gradient-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
        }

        .animated-icon {
            font-size: 30px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .test-actions {
            text-align: center;
            margin: 30px 0;
        }

        .test-actions .btn {
            margin: 0 10px;
        }

        .results-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .test-details ul {
            list-style: none;
            padding: 0;
        }

        .test-details li {
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .test-details li:last-child {
            border-bottom: none;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .design-showcase {
                flex-direction: column;
            }
            
            .test-actions .btn {
                display: block;
                margin: 10px 0;
            }
        }
    </style>

    <script>
        function testEmoji(emoji) {
            const textarea = document.getElementById('emojiTestArea');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const text = textarea.value;
            
            textarea.value = text.substring(0, start) + emoji + text.substring(end);
            textarea.selectionStart = textarea.selectionEnd = start + emoji.length;
            textarea.focus();
        }

        function testFileUpload() {
            const fileInput = document.getElementById('testFileInput');
            const resultDiv = document.getElementById('uploadResult');
            
            if (fileInput.files.length === 0) {
                resultDiv.innerHTML = '<div class="alert alert-warning">Veuillez sélectionner un fichier</div>';
                return;
            }

            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('file', file);

            resultDiv.innerHTML = '<div class="alert alert-info">📤 Upload en cours...</div>';

            fetch('FileUploadHandler.ashx', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `<div class="alert alert-success">✅ Upload réussi: ${data.originalName}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">❌ Erreur: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">❌ Erreur de connexion: ${error}</div>`;
            });
        }

        function testAllFeatures() {
            alert('🚀 Test interactif des fonctionnalités:\n\n' +
                  '✅ Émojis: Cliquez sur les émojis ci-dessus\n' +
                  '✅ Upload: Sélectionnez un fichier et cliquez "Tester Upload"\n' +
                  '✅ Groupes: Visitez la page de gestion des groupes\n' +
                  '✅ Design: Observez les animations et effets hover');
        }
    </script>

</asp:Content>
