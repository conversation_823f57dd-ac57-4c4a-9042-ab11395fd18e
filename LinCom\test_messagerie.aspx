<%@ Page Title="Test Messagerie" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="test_messagerie.aspx.cs" Inherits="LinCom.test_messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="container" style="margin-top: 50px;">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h3>🧪 Test des Améliorations du Module de Messagerie</h3>
                        <p>Cette page teste toutes les nouvelles fonctionnalités implémentées dans LinCom.sln</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Test 1: Vérification des classes -->
                        <div class="test-section">
                            <h4>✅ Test 1: Vérification des Classes et Interfaces</h4>
                            <div class="alert alert-info">
                                <strong>Status:</strong> <asp:Label ID="lblTestClasses" runat="server" Text="En cours..."></asp:Label>
                            </div>
                        </div>

                        <!-- Test 2: Test des nouvelles méthodes -->
                        <div class="test-section">
                            <h4>✅ Test 2: Nouvelles Méthodes de Messagerie</h4>
                            <div class="alert alert-info">
                                <strong>Status:</strong> <asp:Label ID="lblTestMethods" runat="server" Text="En cours..."></asp:Label>
                            </div>
                        </div>

                        <!-- Test 3: Test de la base de données -->
                        <div class="test-section">
                            <h4>✅ Test 3: Connexion Base de Données</h4>
                            <div class="alert alert-info">
                                <strong>Status:</strong> <asp:Label ID="lblTestDB" runat="server" Text="En cours..."></asp:Label>
                            </div>
                        </div>

                        <!-- Test 4: Test des pages -->
                        <div class="test-section">
                            <h4>✅ Test 4: Pages et Navigation</h4>
                            <div class="alert alert-info">
                                <strong>Status:</strong> <asp:Label ID="lblTestPages" runat="server" Text="En cours..."></asp:Label>
                            </div>
                            <div class="mt-3">
                                <a href="messagerie.aspx" class="btn btn-primary">🔗 Tester Messagerie</a>
                                <a href="file/listmessages.aspx" class="btn btn-secondary">🔗 Tester Admin Messages</a>
                            </div>
                        </div>

                        <!-- Test 5: Statistiques -->
                        <div class="test-section">
                            <h4>📊 Test 5: Statistiques du Module</h4>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="stat-box">
                                        <h5>Total Messages</h5>
                                        <h3><asp:Label ID="lblTotalMessages" runat="server" Text="0"></asp:Label></h3>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-box">
                                        <h5>Conversations</h5>
                                        <h3><asp:Label ID="lblTotalConversations" runat="server" Text="0"></asp:Label></h3>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-box">
                                        <h5>Utilisateurs Actifs</h5>
                                        <h3><asp:Label ID="lblUtilisateursActifs" runat="server" Text="0"></asp:Label></h3>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-box">
                                        <h5>Messages Aujourd'hui</h5>
                                        <h3><asp:Label ID="lblMessagesAujourdhui" runat="server" Text="0"></asp:Label></h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bouton de test -->
                        <div class="text-center mt-4">
                            <asp:Button ID="btnRunTests" runat="server" Text="🚀 Exécuter Tous les Tests" 
                                CssClass="btn btn-success btn-lg" OnClick="btnRunTests_Click" />
                        </div>

                        <!-- Résultats des tests -->
                        <div class="test-results mt-4" id="divResults" runat="server" visible="false">
                            <h4>📋 Résultats des Tests</h4>
                            <asp:Literal ID="litResults" runat="server"></asp:Literal>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .stat-box {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            margin-bottom: 10px;
        }
        .stat-box h5 {
            margin-bottom: 10px;
            opacity: 0.8;
        }
        .stat-box h3 {
            margin: 0;
            font-weight: bold;
        }
        .card {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
        }
        .btn {
            margin: 5px;
        }
    </style>
</asp:Content>
