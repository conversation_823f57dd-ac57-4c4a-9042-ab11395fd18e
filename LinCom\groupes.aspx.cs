using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class groupes : System.Web.UI.Page
    {
        IConversation objConversation = new ConversationImp();
        IMembre objMembre = new MembreImp();
        IMessage objMessage = new MessageImp();
        long ide = 0;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Request.Cookies["iduser"] != null)
            {
                ide = Convert.ToInt64(Request.Cookies["iduser"].Value);
            }
            else
            {
                Response.Redirect("login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                ChargerGroupes();
                ChargerMembres();
                ChargerStatistiques();
                ChargerActiviteRecente();
            }
        }

        private void ChargerGroupes()
        {
            try
            {
                using (Connection con = new Connection())
                {
                    var groupes = (from c in con.Conversations
                                  where c.IsGroup == true
                                  select new
                                  {
                                      ConversationId = c.ConversationId,
                                      Sujet = c.Sujet ?? "Groupe sans nom",
                                      Description = c.Description ?? "Aucune description",
                                      CreatedAt = c.CreatedAt ?? DateTime.Now,
                                      NombreParticipants = con.ParticipantConversations
                                          .Count(p => p.ConversationId == c.ConversationId)
                                  }).OrderByDescending(g => g.CreatedAt).ToList();

                    rptGroupes.DataSource = groupes;
                    rptGroupes.DataBind();
                }
            }
            catch (Exception ex)
            {
                // Log l'erreur
                System.Diagnostics.Debug.WriteLine("Erreur lors du chargement des groupes: " + ex.Message);
            }
        }

        private void ChargerMembres()
        {
            try
            {
                using (Connection con = new Connection())
                {
                    var membres = con.Membres
                        .Where(m => m.MembreId != ide) // Exclure l'utilisateur actuel
                        .Select(m => new
                        {
                            MembreId = m.MembreId,
                            NomComplet = m.Nom + " " + m.Prenom
                        })
                        .OrderBy(m => m.NomComplet)
                        .ToList();

                    cblMembres.DataSource = membres;
                    cblMembres.DataTextField = "NomComplet";
                    cblMembres.DataValueField = "MembreId";
                    cblMembres.DataBind();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Erreur lors du chargement des membres: " + ex.Message);
            }
        }

        private void ChargerStatistiques()
        {
            try
            {
                using (Connection con = new Connection())
                {
                    // Total des groupes
                    int totalGroupes = con.Conversations.Count(c => c.IsGroup == true);
                    litTotalGroupes.Text = totalGroupes.ToString();

                    // Groupes actifs (avec au moins un message dans les 7 derniers jours)
                    DateTime semaineDerniere = DateTime.Now.AddDays(-7);
                    int groupesActifs = con.Conversations
                        .Where(c => c.IsGroup == true)
                        .Count(c => con.Messages.Any(m => m.ConversationId == c.ConversationId && 
                                                         m.DateEnvoi >= semaineDerniere));
                    litGroupesActifs.Text = groupesActifs.ToString();

                    // Mes groupes (groupes où je suis participant)
                    int mesGroupes = con.ParticipantConversations
                        .Where(p => p.MembreId == ide)
                        .Join(con.Conversations, p => p.ConversationId, c => c.ConversationId, (p, c) => c)
                        .Count(c => c.IsGroup == true);
                    litMesGroupes.Text = mesGroupes.ToString();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Erreur lors du chargement des statistiques: " + ex.Message);
            }
        }

        private void ChargerActiviteRecente()
        {
            try
            {
                using (Connection con = new Connection())
                {
                    var activiteRecente = (from m in con.Messages
                                          join c in con.Conversations on m.ConversationId equals c.ConversationId
                                          join mb in con.Membres on m.SenderId equals mb.MembreId
                                          where c.IsGroup == true && m.DateEnvoi >= DateTime.Now.AddDays(-7)
                                          orderby m.DateEnvoi descending
                                          select new
                                          {
                                              Expediteur = mb.Nom + " " + mb.Prenom,
                                              Groupe = c.Sujet ?? "Groupe sans nom",
                                              DateEnvoi = m.DateEnvoi
                                          }).Take(10).ToList();

                    rptActiviteRecente.DataSource = activiteRecente;
                    rptActiviteRecente.DataBind();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Erreur lors du chargement de l'activité récente: " + ex.Message);
            }
        }

        protected void btnCreerGroupe_Click(object sender, EventArgs e)
        {
            try
            {
                string nomGroupe = txtNomGroupe.Text.Trim();
                string description = txtDescriptionGroupe.Text.Trim();

                if (string.IsNullOrEmpty(nomGroupe))
                {
                    ScriptManager.RegisterStartupScript(this, GetType(), "Error",
                        "alert('Veuillez entrer un nom pour le groupe.');", true);
                    return;
                }

                // Récupérer les membres sélectionnés
                List<long> membresSelectionnes = new List<long>();
                foreach (ListItem item in cblMembres.Items)
                {
                    if (item.Selected)
                    {
                        membresSelectionnes.Add(Convert.ToInt64(item.Value));
                    }
                }

                if (membresSelectionnes.Count == 0)
                {
                    ScriptManager.RegisterStartupScript(this, GetType(), "Error",
                        "alert('Veuillez sélectionner au moins un membre pour le groupe.');", true);
                    return;
                }

                // Créer le groupe
                using (Connection con = new Connection())
                {
                    // Créer la conversation de groupe
                    var conversation = new Conversation
                    {
                        Sujet = nomGroupe,
                        Description = description,
                        IsGroup = true,
                        CreatedAt = DateTime.Now,
                        CreatedBy = ide
                    };

                    con.Conversations.Add(conversation);
                    con.SaveChanges();

                    // Ajouter le créateur comme participant
                    var participantCreateur = new ParticipantConversation
                    {
                        ConversationId = conversation.ConversationId,
                        MembreId = ide,
                        JoinedAt = DateTime.Now
                    };
                    con.ParticipantConversations.Add(participantCreateur);

                    // Ajouter les membres sélectionnés
                    foreach (long membreId in membresSelectionnes)
                    {
                        var participant = new ParticipantConversation
                        {
                            ConversationId = conversation.ConversationId,
                            MembreId = membreId,
                            JoinedAt = DateTime.Now
                        };
                        con.ParticipantConversations.Add(participant);
                    }

                    con.SaveChanges();

                    // Message de bienvenue automatique
                    var messageBienvenue = new Message
                    {
                        ConversationId = conversation.ConversationId,
                        SenderId = ide,
                        Contenu = $"Bienvenue dans le groupe '{nomGroupe}' ! 🎉",
                        DateEnvoi = DateTime.Now,
                        AttachmentUrl = ""
                    };

                    con.Messages.Add(messageBienvenue);
                    con.SaveChanges();

                    // Créer les statuts de message pour tous les participants
                    var tousParticipants = new List<long>(membresSelectionnes) { ide };
                    foreach (long participantId in tousParticipants)
                    {
                        var messageStatus = new MessageStatu
                        {
                            MessageId = messageBienvenue.MessageId,
                            UserId = participantId,
                            IsRead = participantId == ide ? 1 : 0,
                            ReadAt = participantId == ide ? DateTime.Now : (DateTime?)null
                        };
                        con.MessageStatus.Add(messageStatus);
                    }

                    con.SaveChanges();
                }

                // Réinitialiser le formulaire
                txtNomGroupe.Text = "";
                txtDescriptionGroupe.Text = "";
                foreach (ListItem item in cblMembres.Items)
                {
                    item.Selected = false;
                }

                // Recharger les données
                ChargerGroupes();
                ChargerStatistiques();

                ScriptManager.RegisterStartupScript(this, GetType(), "Success",
                    "$('#createGroupModal').modal('hide'); alert('Groupe créé avec succès !');", true);
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, GetType(), "Error",
                    $"alert('Erreur lors de la création du groupe: {ex.Message}');", true);
            }
        }

        protected void rptGroupes_ItemCommand(object source, RepeaterCommandEventArgs e)
        {
            long groupeId = Convert.ToInt64(e.CommandArgument);

            switch (e.CommandName.ToLower())
            {
                case "voir":
                    Response.Redirect($"messagerie.aspx?id={groupeId}&type=group");
                    break;

                case "modifier":
                    // À implémenter : modal de modification
                    ScriptManager.RegisterStartupScript(this, GetType(), "Info",
                        "alert('Fonctionnalité de modification à implémenter');", true);
                    break;

                case "supprimer":
                    SupprimerGroupe(groupeId);
                    break;
            }
        }

        private void SupprimerGroupe(long groupeId)
        {
            try
            {
                using (Connection con = new Connection())
                {
                    // Supprimer les statuts de messages
                    var messages = con.Messages.Where(m => m.ConversationId == groupeId).ToList();
                    foreach (var message in messages)
                    {
                        var statuts = con.MessageStatus.Where(ms => ms.MessageId == message.MessageId).ToList();
                        con.MessageStatus.RemoveRange(statuts);
                    }

                    // Supprimer les messages
                    con.Messages.RemoveRange(messages);

                    // Supprimer les participants
                    var participants = con.ParticipantConversations.Where(p => p.ConversationId == groupeId).ToList();
                    con.ParticipantConversations.RemoveRange(participants);

                    // Supprimer la conversation
                    var conversation = con.Conversations.FirstOrDefault(c => c.ConversationId == groupeId);
                    if (conversation != null)
                    {
                        con.Conversations.Remove(conversation);
                    }

                    con.SaveChanges();
                }

                ChargerGroupes();
                ChargerStatistiques();

                ScriptManager.RegisterStartupScript(this, GetType(), "Success",
                    "alert('Groupe supprimé avec succès !');", true);
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, GetType(), "Error",
                    $"alert('Erreur lors de la suppression: {ex.Message}');", true);
            }
        }
    }
}
