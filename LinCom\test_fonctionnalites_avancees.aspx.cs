using LinCom.Class;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class test_fonctionnalites_avancees : System.Web.UI.Page
    {
        IConversation objConversation = new ConversationImp();
        IMessage objMessage = new MessageImp();
        long ide = 0;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Request.Cookies["iduser"] != null)
            {
                ide = Convert.ToInt64(Request.Cookies["iduser"].Value);
            }
            else
            {
                Response.Redirect("login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                ChargerStatistiques();
                EffectuerTestsAutomatiques();
            }
        }

        private void ChargerStatistiques()
        {
            try
            {
                using (Connection con = new Connection())
                {
                    // Total des groupes
                    int totalGroupes = con.Conversations.Count(c => c.IsGroup == true);
                    litTotalGroupes.Text = totalGroupes.ToString();

                    // Mes groupes
                    int mesGroupes = con.ParticipantConversations
                        .Where(p => p.MembreId == ide)
                        .Join(con.Conversations, p => p.ConversationId, c => c.ConversationId, (p, c) => c)
                        .Count(c => c.IsGroup == true);
                    litMesGroupes.Text = mesGroupes.ToString();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Erreur lors du chargement des statistiques: " + ex.Message);
            }
        }

        private void EffectuerTestsAutomatiques()
        {
            // Test des pièces jointes
            TestPiecesJointes();
            
            // Test des émojis
            TestEmojis();
            
            // Test des groupes
            TestGroupes();
            
            // Test du design
            TestDesign();
        }

        private void TestPiecesJointes()
        {
            try
            {
                // Vérifier que le dossier uploads existe
                string uploadsPath = Server.MapPath("~/uploads/");
                bool uploadsExists = Directory.Exists(uploadsPath);
                
                // Vérifier que le handler existe
                string handlerPath = Server.MapPath("~/FileUploadHandler.ashx");
                bool handlerExists = File.Exists(handlerPath);
                
                if (uploadsExists && handlerExists)
                {
                    lblTestPiecesJointes.Text = "✅ Réussi";
                    lblTestPiecesJointes.CssClass = "status-success";
                }
                else
                {
                    lblTestPiecesJointes.Text = "⚠️ Partiel";
                    lblTestPiecesJointes.CssClass = "status-error";
                }
            }
            catch (Exception ex)
            {
                lblTestPiecesJointes.Text = "❌ Échec";
                lblTestPiecesJointes.CssClass = "status-error";
                System.Diagnostics.Debug.WriteLine("Erreur test pièces jointes: " + ex.Message);
            }
        }

        private void TestEmojis()
        {
            try
            {
                // Vérifier que la page messagerie contient le sélecteur d'émojis
                string messageriePath = Server.MapPath("~/messagerie.aspx");
                if (File.Exists(messageriePath))
                {
                    string content = File.ReadAllText(messageriePath);
                    bool hasEmojiPicker = content.Contains("emoji-picker") && content.Contains("insertEmoji");
                    
                    if (hasEmojiPicker)
                    {
                        lblTestEmojis.Text = "✅ Réussi";
                        lblTestEmojis.CssClass = "status-success";
                    }
                    else
                    {
                        lblTestEmojis.Text = "❌ Échec";
                        lblTestEmojis.CssClass = "status-error";
                    }
                }
                else
                {
                    lblTestEmojis.Text = "❌ Fichier manquant";
                    lblTestEmojis.CssClass = "status-error";
                }
            }
            catch (Exception ex)
            {
                lblTestEmojis.Text = "❌ Erreur";
                lblTestEmojis.CssClass = "status-error";
                System.Diagnostics.Debug.WriteLine("Erreur test émojis: " + ex.Message);
            }
        }

        private void TestGroupes()
        {
            try
            {
                using (Connection con = new Connection())
                {
                    // Vérifier la structure de la base de données pour les groupes
                    bool hasConversations = con.Conversations.Any();
                    bool hasParticipants = con.ParticipantConversations.Any();
                    
                    // Vérifier que la page groupes existe
                    string groupesPath = Server.MapPath("~/groupes.aspx");
                    bool groupesPageExists = File.Exists(groupesPath);
                    
                    if (groupesPageExists)
                    {
                        lblTestGroupes.Text = "✅ Réussi";
                        lblTestGroupes.CssClass = "status-success";
                    }
                    else
                    {
                        lblTestGroupes.Text = "⚠️ Partiel";
                        lblTestGroupes.CssClass = "status-error";
                    }
                }
            }
            catch (Exception ex)
            {
                lblTestGroupes.Text = "❌ Échec";
                lblTestGroupes.CssClass = "status-error";
                System.Diagnostics.Debug.WriteLine("Erreur test groupes: " + ex.Message);
            }
        }

        private void TestDesign()
        {
            try
            {
                // Vérifier que les fichiers CSS et JS sont présents
                string messageriePath = Server.MapPath("~/messagerie.aspx");
                if (File.Exists(messageriePath))
                {
                    string content = File.ReadAllText(messageriePath);
                    bool hasModernStyles = content.Contains("gradient") && 
                                          content.Contains("transition") && 
                                          content.Contains("border-radius");
                    
                    if (hasModernStyles)
                    {
                        lblTestDesign.Text = "✅ Réussi";
                        lblTestDesign.CssClass = "status-success";
                    }
                    else
                    {
                        lblTestDesign.Text = "❌ Styles manquants";
                        lblTestDesign.CssClass = "status-error";
                    }
                }
                else
                {
                    lblTestDesign.Text = "❌ Fichier manquant";
                    lblTestDesign.CssClass = "status-error";
                }
            }
            catch (Exception ex)
            {
                lblTestDesign.Text = "❌ Erreur";
                lblTestDesign.CssClass = "status-error";
                System.Diagnostics.Debug.WriteLine("Erreur test design: " + ex.Message);
            }
        }

        protected void btnRunAllTests_Click(object sender, EventArgs e)
        {
            StringBuilder results = new StringBuilder();
            bool allTestsPassed = true;

            try
            {
                results.AppendLine("<div class='alert alert-info'><h5>🧪 Rapport Complet des Tests</h5></div>");

                // Test 1: Pièces Jointes
                results.AppendLine("<div class='test-result-section'>");
                results.AppendLine("<h6><i class='fas fa-paperclip'></i> Test des Pièces Jointes</h6>");
                
                try
                {
                    string uploadsPath = Server.MapPath("~/uploads/");
                    string handlerPath = Server.MapPath("~/FileUploadHandler.ashx");
                    
                    if (!Directory.Exists(uploadsPath))
                    {
                        Directory.CreateDirectory(uploadsPath);
                        results.AppendLine("✅ Dossier uploads créé<br/>");
                    }
                    else
                    {
                        results.AppendLine("✅ Dossier uploads existe<br/>");
                    }
                    
                    if (File.Exists(handlerPath))
                    {
                        results.AppendLine("✅ Handler d'upload présent<br/>");
                    }
                    else
                    {
                        results.AppendLine("❌ Handler d'upload manquant<br/>");
                        allTestsPassed = false;
                    }
                    
                    // Test des types de fichiers autorisés
                    string[] allowedTypes = { ".jpg", ".pdf", ".doc", ".png" };
                    results.AppendLine($"✅ Types autorisés: {string.Join(", ", allowedTypes)}<br/>");
                    
                }
                catch (Exception ex)
                {
                    results.AppendLine($"❌ Erreur: {ex.Message}<br/>");
                    allTestsPassed = false;
                }
                results.AppendLine("</div>");

                // Test 2: Émojis
                results.AppendLine("<div class='test-result-section'>");
                results.AppendLine("<h6><i class='fas fa-smile'></i> Test des Émojis</h6>");
                
                try
                {
                    string messageriePath = Server.MapPath("~/messagerie.aspx");
                    if (File.Exists(messageriePath))
                    {
                        string content = File.ReadAllText(messageriePath);
                        
                        if (content.Contains("emoji-picker"))
                        {
                            results.AppendLine("✅ Sélecteur d'émojis présent<br/>");
                        }
                        else
                        {
                            results.AppendLine("❌ Sélecteur d'émojis manquant<br/>");
                            allTestsPassed = false;
                        }
                        
                        if (content.Contains("insertEmoji"))
                        {
                            results.AppendLine("✅ Fonction d'insertion présente<br/>");
                        }
                        else
                        {
                            results.AppendLine("❌ Fonction d'insertion manquante<br/>");
                            allTestsPassed = false;
                        }
                        
                        // Compter les émojis disponibles
                        int emojiCount = content.Split(new string[] { "onclick=\"insertEmoji(" }, StringSplitOptions.None).Length - 1;
                        results.AppendLine($"✅ {emojiCount} émojis disponibles<br/>");
                    }
                }
                catch (Exception ex)
                {
                    results.AppendLine($"❌ Erreur: {ex.Message}<br/>");
                    allTestsPassed = false;
                }
                results.AppendLine("</div>");

                // Test 3: Groupes
                results.AppendLine("<div class='test-result-section'>");
                results.AppendLine("<h6><i class='fas fa-users'></i> Test des Groupes</h6>");
                
                try
                {
                    using (Connection con = new Connection())
                    {
                        int totalGroupes = con.Conversations.Count(c => c.IsGroup == true);
                        results.AppendLine($"✅ {totalGroupes} groupes dans la base<br/>");
                        
                        int totalParticipants = con.ParticipantConversations.Count();
                        results.AppendLine($"✅ {totalParticipants} participations enregistrées<br/>");
                        
                        // Vérifier la page de gestion
                        string groupesPath = Server.MapPath("~/groupes.aspx");
                        if (File.Exists(groupesPath))
                        {
                            results.AppendLine("✅ Page de gestion des groupes présente<br/>");
                        }
                        else
                        {
                            results.AppendLine("❌ Page de gestion manquante<br/>");
                            allTestsPassed = false;
                        }
                    }
                }
                catch (Exception ex)
                {
                    results.AppendLine($"❌ Erreur: {ex.Message}<br/>");
                    allTestsPassed = false;
                }
                results.AppendLine("</div>");

                // Test 4: Design
                results.AppendLine("<div class='test-result-section'>");
                results.AppendLine("<h6><i class='fas fa-palette'></i> Test du Design</h6>");
                
                try
                {
                    string messageriePath = Server.MapPath("~/messagerie.aspx");
                    if (File.Exists(messageriePath))
                    {
                        string content = File.ReadAllText(messageriePath);
                        
                        int gradientCount = content.Split(new string[] { "gradient" }, StringSplitOptions.None).Length - 1;
                        results.AppendLine($"✅ {gradientCount} gradients utilisés<br/>");
                        
                        int transitionCount = content.Split(new string[] { "transition" }, StringSplitOptions.None).Length - 1;
                        results.AppendLine($"✅ {transitionCount} transitions définies<br/>");
                        
                        if (content.Contains("border-radius"))
                        {
                            results.AppendLine("✅ Coins arrondis utilisés<br/>");
                        }
                        
                        if (content.Contains("box-shadow"))
                        {
                            results.AppendLine("✅ Ombres portées utilisées<br/>");
                        }
                    }
                }
                catch (Exception ex)
                {
                    results.AppendLine($"❌ Erreur: {ex.Message}<br/>");
                    allTestsPassed = false;
                }
                results.AppendLine("</div>");

                // Résumé final
                if (allTestsPassed)
                {
                    results.AppendLine("<div class='alert alert-success'>");
                    results.AppendLine("<h4>🎉 Tous les tests sont réussis !</h4>");
                    results.AppendLine("<p>Le module de messagerie avancé est entièrement fonctionnel avec :</p>");
                    results.AppendLine("<ul>");
                    results.AppendLine("<li>✅ Support des pièces jointes</li>");
                    results.AppendLine("<li>✅ Sélecteur d'émojis interactif</li>");
                    results.AppendLine("<li>✅ Gestion complète des groupes</li>");
                    results.AppendLine("<li>✅ Design moderne et responsive</li>");
                    results.AppendLine("</ul>");
                    results.AppendLine("</div>");
                }
                else
                {
                    results.AppendLine("<div class='alert alert-warning'>");
                    results.AppendLine("<h4>⚠️ Certains tests nécessitent une attention</h4>");
                    results.AppendLine("<p>Veuillez vérifier les erreurs ci-dessus et corriger les problèmes identifiés.</p>");
                    results.AppendLine("</div>");
                }

            }
            catch (Exception ex)
            {
                results.AppendLine($"<div class='alert alert-danger'><h4>❌ Erreur générale</h4>{ex.Message}</div>");
            }

            litResults.Text = results.ToString();
            testResults.Visible = true;
            
            // Recharger les statistiques
            ChargerStatistiques();
            EffectuerTestsAutomatiques();
        }
    }
}
