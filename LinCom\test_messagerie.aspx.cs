using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class test_messagerie : System.Web.UI.Page
    {
        IMessage objMessage = new MessageImp();
        IConversation objConversation = new ConversationImp();
        IMembre objMembre = new MembreImp();
        
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                ChargerStatistiques();
            }
        }

        protected void btnRunTests_Click(object sender, EventArgs e)
        {
            StringBuilder results = new StringBuilder();
            bool allTestsPassed = true;

            try
            {
                // Test 1: Vérification des classes
                results.AppendLine("<div class='alert alert-info'><h5>🧪 Test 1: Vérification des Classes</h5>");
                
                try
                {
                    var messageService = new MessageImp();
                    var conversationService = new ConversationImp();
                    results.AppendLine("✅ Classes MessageImp et ConversationImp instanciées avec succès<br/>");
                    lblTestClasses.Text = "✅ Réussi";
                    lblTestClasses.CssClass = "text-success";
                }
                catch (Exception ex)
                {
                    results.AppendLine($"❌ Erreur lors de l'instanciation des classes: {ex.Message}<br/>");
                    lblTestClasses.Text = "❌ Échec";
                    lblTestClasses.CssClass = "text-danger";
                    allTestsPassed = false;
                }
                results.AppendLine("</div>");

                // Test 2: Test des nouvelles méthodes
                results.AppendLine("<div class='alert alert-info'><h5>🧪 Test 2: Nouvelles Méthodes</h5>");
                
                try
                {
                    // Test de la méthode CompterNonLus
                    int nonLus = objMessage.CompterNonLus(1); // Test avec ID 1
                    results.AppendLine($"✅ Méthode CompterNonLus fonctionne: {nonLus} messages non lus<br/>");
                    
                    // Test de la méthode VerifierConversationId
                    long conversationId = objConversation.VerifierConversationId(1, 2);
                    results.AppendLine($"✅ Méthode VerifierConversationId fonctionne: ID {conversationId}<br/>");
                    
                    lblTestMethods.Text = "✅ Réussi";
                    lblTestMethods.CssClass = "text-success";
                }
                catch (Exception ex)
                {
                    results.AppendLine($"❌ Erreur lors du test des méthodes: {ex.Message}<br/>");
                    lblTestMethods.Text = "❌ Échec";
                    lblTestMethods.CssClass = "text-danger";
                    allTestsPassed = false;
                }
                results.AppendLine("</div>");

                // Test 3: Test de la base de données
                results.AppendLine("<div class='alert alert-info'><h5>🧪 Test 3: Base de Données</h5>");
                
                try
                {
                    using (Connection con = new Connection())
                    {
                        int totalMessages = con.Messages.Count();
                        int totalConversations = con.Conversations.Count();
                        int totalMembres = con.Membres.Count();
                        
                        results.AppendLine($"✅ Connexion à la base de données réussie<br/>");
                        results.AppendLine($"📊 {totalMessages} messages, {totalConversations} conversations, {totalMembres} membres<br/>");
                        
                        lblTestDB.Text = "✅ Réussi";
                        lblTestDB.CssClass = "text-success";
                    }
                }
                catch (Exception ex)
                {
                    results.AppendLine($"❌ Erreur de connexion à la base de données: {ex.Message}<br/>");
                    lblTestDB.Text = "❌ Échec";
                    lblTestDB.CssClass = "text-danger";
                    allTestsPassed = false;
                }
                results.AppendLine("</div>");

                // Test 4: Test des pages
                results.AppendLine("<div class='alert alert-info'><h5>🧪 Test 4: Pages et Navigation</h5>");
                
                try
                {
                    // Vérifier que les pages existent
                    bool messagerieExists = System.IO.File.Exists(Server.MapPath("~/messagerie.aspx"));
                    bool adminExists = System.IO.File.Exists(Server.MapPath("~/file/listmessages.aspx"));
                    
                    if (messagerieExists && adminExists)
                    {
                        results.AppendLine("✅ Pages messagerie.aspx et listmessages.aspx trouvées<br/>");
                        lblTestPages.Text = "✅ Réussi";
                        lblTestPages.CssClass = "text-success";
                    }
                    else
                    {
                        results.AppendLine($"❌ Pages manquantes - Messagerie: {messagerieExists}, Admin: {adminExists}<br/>");
                        lblTestPages.Text = "❌ Échec";
                        lblTestPages.CssClass = "text-danger";
                        allTestsPassed = false;
                    }
                }
                catch (Exception ex)
                {
                    results.AppendLine($"❌ Erreur lors de la vérification des pages: {ex.Message}<br/>");
                    lblTestPages.Text = "❌ Échec";
                    lblTestPages.CssClass = "text-danger";
                    allTestsPassed = false;
                }
                results.AppendLine("</div>");

                // Résumé final
                if (allTestsPassed)
                {
                    results.AppendLine("<div class='alert alert-success'><h4>🎉 Tous les tests sont réussis !</h4>");
                    results.AppendLine("Le module de messagerie est correctement intégré dans LinCom.sln</div>");
                }
                else
                {
                    results.AppendLine("<div class='alert alert-warning'><h4>⚠️ Certains tests ont échoué</h4>");
                    results.AppendLine("Veuillez vérifier les erreurs ci-dessus</div>");
                }

            }
            catch (Exception ex)
            {
                results.AppendLine($"<div class='alert alert-danger'><h4>❌ Erreur générale</h4>{ex.Message}</div>");
            }

            litResults.Text = results.ToString();
            divResults.Visible = true;
            
            // Recharger les statistiques
            ChargerStatistiques();
        }

        private void ChargerStatistiques()
        {
            try
            {
                using (Connection con = new Connection())
                {
                    // Total des messages
                    int totalMessages = con.Messages.Count();
                    lblTotalMessages.Text = totalMessages.ToString();

                    // Total des conversations
                    int totalConversations = con.Conversations.Count();
                    lblTotalConversations.Text = totalConversations.ToString();

                    // Messages d'aujourd'hui
                    DateTime aujourd_hui = DateTime.Today;
                    int messagesAujourdhui = con.Messages.Count(m => m.DateEnvoi.HasValue && 
                                                                    m.DateEnvoi.Value.Date == aujourd_hui);
                    lblMessagesAujourdhui.Text = messagesAujourdhui.ToString();

                    // Utilisateurs actifs (qui ont envoyé au moins un message dans les 7 derniers jours)
                    DateTime semaineDerniere = DateTime.Now.AddDays(-7);
                    int utilisateursActifs = con.Messages
                        .Where(m => m.DateEnvoi.HasValue && m.DateEnvoi.Value >= semaineDerniere)
                        .Select(m => m.SenderId)
                        .Distinct()
                        .Count();
                    lblUtilisateursActifs.Text = utilisateursActifs.ToString();
                }
            }
            catch (Exception ex)
            {
                lblTotalMessages.Text = "Erreur";
                lblTotalConversations.Text = "Erreur";
                lblMessagesAujourdhui.Text = "Erreur";
                lblUtilisateursActifs.Text = "Erreur";
            }
        }
    }
}
