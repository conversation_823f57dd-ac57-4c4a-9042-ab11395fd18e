﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics.Eventing.Reader;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class messagerie : System.Web.UI.Page
    {
        IMessage objmes = new MessageImp();
        Message_Class mess = new Message_Class();
        IConversation objconver = new ConversationImp();
        Conversation_Class conver = new Conversation_Class();
        ParticipantConversation_Class partconver = new ParticipantConversation_Class();
        MessageStatus_Class messtatu = new MessageStatus_Class();

        Organisation_Class org = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        IPoste objpost = new PosteImp();
        Post_Class post = new Post_Class();
        Post_Class pos = new Post_Class();
        IDomainePost objdompost = new DomainePostImp();
        DomainePost_Class dompost = new DomainePost_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objmem = new MembreImp();
        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        ICommonCode co = new CommonCode();
        //   UrlPartage = $"{Request.Url.GetLeftPart(UriPartial.Authority)}/post/" + ep.name,
        IDomaineInterventionOrganisation objdomorg = new DomaineInterventionOrganisationImp();
        DomaineInterventionOrganisation_Class domorg = new DomaineInterventionOrganisation_Class();
        int info;
        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;
        static long conversationreceveur;
        protected void Page_Load(object sender, EventArgs e)
        {
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin
                long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte
                rolid = Convert.ToInt32(role.Value);
            }
            else
            {
                // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
                Response.Redirect("~/login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                // Par défaut, tu peux initialiser avec une conversation
                ChargerMessages();
                AppelMethode();
                ChargerMembresDisponibles();
            }
        }
        public void AppelMethode()
        {
            objmem.ChargerListview(listmembre,-1,"actif","");

          
        }
     

        protected void listmembre_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            if (e.CommandName == "viewmem")
            {
                long idMembre =Convert.ToInt64( e.CommandArgument.ToString());
                // Utilisez l'ID pour récupérer les détails du membre
                objmem.AfficherDetails(idMembre,mem);
               
                // Changez le titre de la discussion
                lblHeader.Text = "Message envoyé à " + mem.Nom+" "+mem.Prenom;
                lblId.Text = mem.MembreId.ToString();

                ChargerMessages();

            }
        }

    private void CreationConversation(int cd,string sujetgroup)
        {//creation d'une nouvelle convrsation

            if (cd==0)
            {//privee
                conver.Sujet = "";
                conver.IsGroup = 0;
                conver.CreatedAt = DateTime.Now;
                objconver.Creer(conver);
            }
            else if (cd==1)
            {
                //equipe
                conver.Sujet = sujetgroup;
                conver.IsGroup = 1;
                conver.CreatedAt = DateTime.Now;
                objconver.Creer(conver);
            }
              
        }
      private  void CreationParticipantConversation(long memparticipant)
        {//creation des membres qui commencent le tchat
         //long conversationreceveur = objconver.VerifierConversationId(ide, Convert.ToInt64(memparticipant));
         //if (conversationreceveur > 0)
         //{
         //    partconver.ConversationId = conversationreceveur;
         //    partconver.MembreId= memparticipant;
         //    partconver.JoinedAt = DateTime.Now;

            //    objconver.AjouterParticipant(conversationreceveur, Convert.ToInt64(memparticipant));

            //}
            conversationreceveur = objconver.VerifierConversationId(ide, memparticipant);

            // Si aucune conversation => on la crée
            if (conversationreceveur <= 0)
            {
                CreationConversation(0, ""); // conversation privée
                conversationreceveur = objconver.VerifierConversationId(ide, memparticipant);
            }
            else
            {
                partconver.ConversationId = conversationreceveur;
                partconver.MembreId = memparticipant;
                partconver.JoinedAt = DateTime.Now;
                // Ensuite on ajoute les 2 participants S'ILS NE SONT PAS DÉJÀ DEDANS

                if (!objconver.ParticipantExiste(conversationreceveur, ide))
                    objconver.AjouterParticipant(conversationreceveur, ide);

                if (!objconver.ParticipantExiste(conversationreceveur, memparticipant))
                    objconver.AjouterParticipant(conversationreceveur, memparticipant);

            }

        }
        private int CreationMessage(long convID,long membrId)
        {
            mess.ConversationId = convID;
            mess.SenderId = membrId;
            mess.Contenu = txtMessage.Value;
            mess.DateEnvoi = DateTime.Now;
            mess.name = "";

            // Support des pièces jointes
            mess.AttachmentUrl = hdnAttachmentUrl.Value ?? "";

            info=objmes.Envoyer(mess);

            return info;
        }

        private int CreationMessageGroupe(long groupeId, long membrId)
        {
            // Pour les groupes, on utilise directement l'ID du groupe comme ConversationId
            mess.ConversationId = groupeId;
            mess.SenderId = membrId;
            mess.Contenu = txtMessage.Value;
            mess.DateEnvoi = DateTime.Now;
            mess.name = "";
            mess.AttachmentUrl = hdnAttachmentUrl.Value ?? "";

            info = objmes.Envoyer(mess);

            // Si le message est envoyé avec succès, créer les statuts pour tous les participants du groupe
            if (info == 1)
            {
                CreerStatutsMessageGroupe(groupeId, membrId);
            }

            return info;
        }

        private void CreerStatutsMessageGroupe(long groupeId, long expediteurId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    // Récupérer tous les participants du groupe
                    var participants = con.ParticipantConversations
                        .Where(p => p.ConversationId == groupeId)
                        .Select(p => p.MembreId)
                        .ToList();

                    // Récupérer l'ID du dernier message envoyé
                    var dernierMessage = con.Messages
                        .Where(m => m.ConversationId == groupeId && m.SenderId == expediteurId)
                        .OrderByDescending(m => m.DateEnvoi)
                        .FirstOrDefault();

                    if (dernierMessage != null)
                    {
                        foreach (var participantId in participants)
                        {
                            var messageStatus = new MessageStatu
                            {
                                MessageId = dernierMessage.MessageId,
                                UserId = participantId.Value,
                                IsRead = participantId == expediteurId ? 1 : 0, // L'expéditeur a déjà "lu" son message
                                ReadAt = participantId == expediteurId ? DateTime.Now : (DateTime?)null
                            };

                            con.MessageStatus.Add(messageStatus);
                        }

                        con.SaveChanges();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine("Erreur lors de la création des statuts de groupe: " + ex.Message);
                }
            }
        }

        private int CreationMessagestatus(long convID, long membrId,int lire)
        {
            messtatu.MessageId = convID;
            messtatu.UserId = membrId;
            messtatu.IsRead = lire;
            messtatu.ReadAt = DateTime.Now;
           
            info =objmes.EnvoyerMessageStatus(messtatu);

            return info;
        }

        private void EnvoieMessagerie()
        {
            // Validation des entrées
            string message = txtMessage.Value.Trim();
            if (string.IsNullOrEmpty(message))
            {
                return; // Ne pas envoyer de message vide
            }

            // Limiter la taille du message
            if (message.Length > 2000)
            {
                message = message.Substring(0, 2000);
                // Informer l'utilisateur que le message a été tronqué
                ScriptManager.RegisterStartupScript(this, GetType(), "MessageTruncated",
                    "alert('Votre message a été tronqué car il dépassait la limite de 2000 caractères.');", true);
            }

            // Échapper les caractères HTML pour éviter les attaques XSS
            message = HttpUtility.HtmlEncode(message);
            txtMessage.Value = message;

            long senderId = ide;
            long destinataireId = Convert.ToInt64(lblId.Text);
            bool isGroup = hdnIsGroup.Value == "1";
            int info=0,info1 = 0,info2=0;

            if (isGroup)
            {
                // Groupe : conversation déjà existante via id du groupe
                long idGroupe = destinataireId;

                // Créer le message pour le groupe
                info = CreationMessageGroupe(idGroupe, senderId);

                if (info == 1)
                {
                    ChargerMessages();
                    // Vider les champs après envoi réussi
                    txtMessage.Value = "";
                    hdnAttachmentUrl.Value = "";

                    // Masquer la prévisualisation du fichier
                    ScriptManager.RegisterStartupScript(this, GetType(), "GroupMessageSent",
                        "document.getElementById('filePreview').style.display = 'none'; " +
                        "document.getElementById('fileInput').value = ''; " +
                        "console.log('Message de groupe envoyé avec succès');", true);
                }
            }
            else
            {
                CreationParticipantConversation(destinataireId);

                // Tchat privé
                long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                //if (conversationId <= 0)
                //    CreationConversation(0, "");


                // conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                info=CreationMessage(conversationId,senderId);
                info1=CreationMessagestatus(conversationId,senderId,1);
                info2=CreationMessagestatus(conversationId,destinataireId,0);

                if (info == 1 && info1==1 && info2==1)
                {
                    ChargerMessages();
                    // Vider les champs après envoi réussi
                    txtMessage.Value = "";
                    hdnAttachmentUrl.Value = "";

                    // Masquer la prévisualisation du fichier
                    ScriptManager.RegisterStartupScript(this, GetType(), "MessageSent",
                        "document.getElementById('filePreview').style.display = 'none'; " +
                        "document.getElementById('fileInput').value = ''; " +
                        "console.log('Message envoyé avec succès');", true);
                }
            }

            if (info != 1)
                Response.Write("<script>alert('Erreur lors de l’envoi du message.');</script>");
        }
        protected void btnenvoie_ServerClick(object sender, EventArgs e)
        {
            EnvoieMessagerie();
        }
        void EnvoieMessage()
        {
            if (!string.IsNullOrWhiteSpace(txtMessage.Value))
            {
                long conversationreceveurmembre = objconver.VerifierConversationId(ide, Convert.ToInt64(lblId.Text));
               
                if (conversationreceveurmembre <= 0)
                    CreationConversation(0,"");

                long conversationreceveurmembreencore = objconver.VerifierConversationId(ide, Convert.ToInt64(lblId.Text));

                mess.ConversationId = conversationreceveurmembreencore;
                mess.SenderId = ide;
                mess.Contenu = txtMessage.Value.Trim();
                mess.DateEnvoi = DateTime.Now;
                mess.name = "";
                mess.AttachmentUrl = null;

               info= objmes.Envoyer(mess);

                if (info==1)
                {
                    //CreationParticipantConversation();

                 //   Response.Write("<script LANGUAGE=JavaScript>alert('Message envoyé')</script>");

                }
                else
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Erreur')</script>");


                }
            }
        }
        void ChargerMessages()
        {
            //chargement des messages
            long senderId = ide;
            long destinataireId = Convert.ToInt64(lblId.Text);
            long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

            objmes.ChargerMessages(rptMessages, conversationId, 1000);

            // Marquer la conversation comme lue pour l'utilisateur connecté
            objmes.MarquerConversationCommeLue(conversationId, senderId);
        }
        // ⚠️ À remplacer avec le vrai utilisateur connecté

        protected void btnRechercherContact_Click(object sender, EventArgs e)
        {
            string termeRecherche = txtRechercheContact.Text.Trim();
            if (!string.IsNullOrEmpty(termeRecherche))
            {
                // Rechercher dans les contacts
                RechercherContacts(termeRecherche);
            }
            else
            {
                // Recharger tous les contacts
                AppelMethode();
            }
        }

        private void RechercherContacts(string termeRecherche)
        {
            using (Connection con = new Connection())
            {
                var contacts = from m in con.Membres
                              where (m.Nom.Contains(termeRecherche) ||
                                    m.Prenom.Contains(termeRecherche) ||
                                    m.Email.Contains(termeRecherche)) &&
                                    m.MembreId != ide // Exclure l'utilisateur connecté
                              select new
                              {
                                  id = m.MembreId,
                                  Membre = (m.Nom ?? "") + " " + (m.Prenom ?? ""),
                                  PhotoProfil = m.PhotoProfil ?? "default.png"
                              };

                listmembre.DataSource = contacts.Take(20).ToList();
                listmembre.DataBind();
            }
        }

        // Méthodes helper pour l'affichage des fichiers
        protected string GetFileIcon(string filePath)
        {
            if (string.IsNullOrEmpty(filePath)) return "";

            string extension = System.IO.Path.GetExtension(filePath).ToLower();
            switch (extension)
            {
                case ".pdf":
                    return "📄";
                case ".doc":
                case ".docx":
                    return "📝";
                case ".xls":
                case ".xlsx":
                    return "📊";
                case ".ppt":
                case ".pptx":
                    return "📋";
                case ".jpg":
                case ".jpeg":
                case ".png":
                case ".gif":
                    return "🖼️";
                case ".mp4":
                case ".avi":
                case ".mov":
                    return "🎥";
                case ".mp3":
                case ".wav":
                    return "🎵";
                case ".zip":
                case ".rar":
                    return "🗜️";
                default:
                    return "📎";
            }
        }

        protected string GetFileName(string filePath)
        {
            if (string.IsNullOrEmpty(filePath)) return "";
            return System.IO.Path.GetFileName(filePath);
        }

        // Méthodes spécifiques pour les expressions de liaison dans les Repeaters
        protected string GetFileIconForMessage(object attachmentUrl)
        {
            if (attachmentUrl == null) return "";
            return GetFileIcon(attachmentUrl.ToString());
        }

        protected string GetFileNameForMessage(object attachmentUrl)
        {
            if (attachmentUrl == null) return "";
            return GetFileName(attachmentUrl.ToString());
        }

        // Méthode pour obtenir l'ID de l'utilisateur connecté
        public long GetCurrentUserId()
        {
            return ide;
        }

        // Méthode pour déterminer si le message appartient à l'utilisateur connecté
        public bool IsCurrentUserMessage(object senderId)
        {
            if (senderId == null) return false;
            try
            {
                long messageUserId = Convert.ToInt64(senderId);
                return messageUserId == ide;
            }
            catch
            {
                return false;
            }
        }

        // Méthode sécurisée pour obtenir le nom de fichier
        public string GetFileNameSafe(object attachmentUrl)
        {
            if (attachmentUrl == null || string.IsNullOrEmpty(attachmentUrl.ToString()))
                return "Fichier";

            try
            {
                return System.IO.Path.GetFileName(attachmentUrl.ToString());
            }
            catch
            {
                return "Fichier";
            }
        }

        // Méthodes pour la gestion des groupes
        private void ChargerMembresDisponibles()
        {
            try
            {
                using (Connection con = new Connection())
                {
                    var membres = con.Membres
                        .Where(m => m.MembreId != ide) // Exclure l'utilisateur actuel
                        .Select(m => new
                        {
                            MembreId = m.MembreId,
                            NomComplet = m.Nom + " " + m.Prenom
                        })
                        .OrderBy(m => m.NomComplet)
                        .ToList();

                    cblMembres.DataSource = membres;
                    cblMembres.DataTextField = "NomComplet";
                    cblMembres.DataValueField = "MembreId";
                    cblMembres.DataBind();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Erreur lors du chargement des membres: " + ex.Message);
            }
        }

        public void btnCreerGroupe_Click(object sender, EventArgs e)
        {
            try
            {
                string nomGroupe = txtNomGroupe.Text.Trim();
                string description = txtDescriptionGroupe.Text.Trim();

                if (string.IsNullOrEmpty(nomGroupe))
                {
                    ScriptManager.RegisterStartupScript(this, GetType(), "Error",
                        "alert('Veuillez entrer un nom pour le groupe.');", true);
                    return;
                }

                // Récupérer les membres sélectionnés
                List<long> membresSelectionnes = new List<long>();
                foreach (ListItem item in cblMembres.Items)
                {
                    if (item.Selected)
                    {
                        membresSelectionnes.Add(Convert.ToInt64(item.Value));
                    }
                }

                if (membresSelectionnes.Count == 0)
                {
                    ScriptManager.RegisterStartupScript(this, GetType(), "Error",
                        "alert('Veuillez sélectionner au moins un membre pour le groupe.');", true);
                    return;
                }

                // Créer le groupe
                using (Connection con = new Connection())
                {
                    // Créer la conversation de groupe
                    var conversation = new Conversation
                    {
                        Sujet = nomGroupe,
                        Description = description,
                        IsGroup = true,
                        CreatedAt = DateTime.Now,
                        CreatedBy = ide
                    };

                    con.Conversations.Add(conversation);
                    con.SaveChanges();

                    // Ajouter le créateur comme participant
                    var participantCreateur = new ParticipantConversation
                    {
                        ConversationId = conversation.ConversationId,
                        MembreId = ide,
                        JoinedAt = DateTime.Now
                    };
                    con.ParticipantConversations.Add(participantCreateur);

                    // Ajouter les membres sélectionnés
                    foreach (long membreId in membresSelectionnes)
                    {
                        var participant = new ParticipantConversation
                        {
                            ConversationId = conversation.ConversationId,
                            MembreId = membreId,
                            JoinedAt = DateTime.Now
                        };
                        con.ParticipantConversations.Add(participant);
                    }

                    con.SaveChanges();

                    // Message de bienvenue automatique
                    var messageBienvenue = new Message
                    {
                        ConversationId = conversation.ConversationId,
                        SenderId = ide,
                        Contenu = $"🎉 Bienvenue dans le groupe '{nomGroupe}' ! Vous pouvez maintenant commencer à discuter.",
                        DateEnvoi = DateTime.Now,
                        AttachmentUrl = ""
                    };

                    con.Messages.Add(messageBienvenue);
                    con.SaveChanges();

                    // Créer les statuts de message pour tous les participants
                    var tousParticipants = new List<long>(membresSelectionnes) { ide };
                    foreach (long participantId in tousParticipants)
                    {
                        var messageStatus = new MessageStatu
                        {
                            MessageId = messageBienvenue.MessageId,
                            UserId = participantId,
                            IsRead = participantId == ide ? 1 : 0,
                            ReadAt = participantId == ide ? DateTime.Now : (DateTime?)null
                        };
                        con.MessageStatus.Add(messageStatus);
                    }

                    con.SaveChanges();
                }

                // Réinitialiser le formulaire
                txtNomGroupe.Text = "";
                txtDescriptionGroupe.Text = "";
                foreach (ListItem item in cblMembres.Items)
                {
                    item.Selected = false;
                }

                // Rediriger vers le groupe créé
                ScriptManager.RegisterStartupScript(this, GetType(), "Success",
                    "$('#createGroupModal').modal('hide'); " +
                    "alert('Groupe créé avec succès ! Vous allez être redirigé vers le groupe.'); " +
                    "setTimeout(() => { window.location.href = 'groupes.aspx'; }, 2000);", true);
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, GetType(), "Error",
                    $"alert('Erreur lors de la création du groupe: {ex.Message}');", true);
            }
        }

    }
}