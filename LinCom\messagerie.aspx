﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie.aspx.cs" Inherits="LinCom.messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="#">Espace FNUAP</a></li>
                    <li><a href="#">Bibliothèque Digitale de FNUAP</a></li>
                    <li class="current"><a href="ressources-document.aspx">Documents officiels</a></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="main">
        <asp:HiddenField ID="hdnConversationId" runat="server" />
<asp:HiddenField ID="hdnIsGroup" runat="server" />
<asp:HiddenField ID="hdnCurrentUserId" runat="server" />

        <div class="container py-4">
            <h2 class="mb-4">💬 Espace Messagerie</h2>

            <div class="chat-wrapper">

                <!-- Sidebar -->
                <div class="contacts-panel">
                    <div class="contacts-header">👥 Contacts</div>
                    <div class="contacts-search">
                        <div class="search-container">
                            <div class="search-box">
                                <i class="fas fa-search search-icon"></i>
                                <asp:TextBox ID="txtRechercheContact" runat="server" CssClass="search-input-modern"
                                    placeholder="Rechercher un contact..." onkeyup="filterContacts(this.value)"></asp:TextBox>
                                <asp:Button ID="btnRechercherContact" runat="server" Text=""
                                    CssClass="search-btn-modern" OnClick="btnRechercherContact_Click"
                                    title="Rechercher">
                                    <i class="fas fa-search"></i>
                                </asp:Button>
                            </div>
                        </div>
                        <div class="quick-actions-modern">
                            <button type="button" class="new-group-btn" onclick="showCreateGroupModal()">
                                <i class="fas fa-users"></i>
                                <span>Nouveau Groupe</span>
                            </button>
                            <button type="button" class="refresh-btn" onclick="refreshContacts()" title="Actualiser">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <asp:ListView ID="listmembre" runat="server" OnItemCommand="listmembre_ItemCommand">
                        <EmptyDataTemplate>Aucune Donnée</EmptyDataTemplate>
                        <ItemTemplate>
                            <asp:LinkButton ID="btnSelectMembre" runat="server"
                                CommandName="viewmem"
                                CommandArgument='<%# Eval("id") %>'
                                CssClass="contact-item">
        <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/", Eval("PhotoProfil"))) %>' alt="Nom du Membre" />
        <div>
            <div class="contact-name"><%# HttpUtility.HtmlEncode(Eval("Membre")) %></div>
        </div>
                            </asp:LinkButton>
                                <!-- 🔽 Champ caché pour dire si c’est un groupe -->
 
                        </ItemTemplate>
                    </asp:ListView>

                </div>


                <!-- Main Chat Area -->
                <div class="chat-panel">

                    <div class="chat-header">
                        <asp:Label ID="lblHeader" runat="server" Text="Sélectionnez un contact pour discuter"></asp:Label>
                        <asp:Label ID="lblId" Visible="false" runat="server" Text="0"></asp:Label>
                    </div>

                    <div class="chat-body">
                      <asp:Repeater ID="rptMessages" runat="server">
    <ItemTemplate>
        <div class='message-bubble <%# IsCurrentUserMessage(Eval("SenderId")) ? "message-sent" : "message-received" %>'>
            <div class="message-content">
                <div class="message-header">
                    <div class="user-info">
                        <img class="user-avatar" src='<%# ResolveUrl("~/file/membr/") + Eval("Photomembre") %>'
                             alt="Photo" onerror="this.src='~/asset/img/default-avatar.png'" />
                        <div class="user-details">
                            <span class="user-name"><%# Eval("Expediteur") %></span>
                            <span class="message-time"><%# Eval("DateEnvoi", "{0:HH:mm}") %></span>
                        </div>
                    </div>
                    <div class="message-status">
                        <%# IsCurrentUserMessage(Eval("SenderId")) ? "<i class='fas fa-check message-check'></i>" : "" %>
                    </div>
                </div>

                <div class="message-text">
                    <%# Eval("Contenu") %>
                </div>

                <%-- Pièce jointe avec design amélioré --%>
                <asp:PlaceHolder runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) %>'>
                    <div class="attachment-container">
                        <div class="attachment-preview">
                            <div class="attachment-icon">
                                <i class="fas fa-file"></i>
                            </div>
                            <div class="attachment-details">
                                <div class="attachment-name"><%# GetFileNameSafe(Eval("AttachmentUrl")) %></div>
                                <div class="attachment-meta">Pièce jointe</div>
                            </div>
                            <a href='<%# Eval("AttachmentUrl") %>' target="_blank" class="attachment-download" title="Télécharger">
                                <i class="fas fa-download"></i>
                            </a>
                        </div>
                    </div>
                </asp:PlaceHolder>

                <div class="message-footer">
                    <span class="message-date"><%# Eval("DateEnvoi", "{0:dd/MM/yyyy}") %></span>
                </div>
            </div>
        </div>
    </ItemTemplate>
</asp:Repeater>

                    </div>

                    <div class="chat-footer">
                        <!-- Zone de prévisualisation des fichiers -->
                        <div id="filePreview" class="file-preview" style="display: none;">
                            <div class="preview-item">
                                <div class="file-info">
                                    <i class="fas fa-file"></i>
                                    <span class="file-name"></span>
                                </div>
                                <button type="button" class="remove-file" onclick="removeFile()" title="Supprimer">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Sélecteur d'émojis -->
                        <div id="emojiPicker" class="emoji-picker" style="display: none;">
                            <div class="emoji-header">
                                <span>Choisir un émoji</span>
                                <button type="button" onclick="toggleEmojiPicker()" class="close-emoji">×</button>
                            </div>
                            <div class="emoji-grid">
                                <span class="emoji" onclick="insertEmoji('😊')" title="Sourire">😊</span>
                                <span class="emoji" onclick="insertEmoji('😂')" title="Rire">😂</span>
                                <span class="emoji" onclick="insertEmoji('❤️')" title="Cœur">❤️</span>
                                <span class="emoji" onclick="insertEmoji('👍')" title="Pouce en l'air">👍</span>
                                <span class="emoji" onclick="insertEmoji('👎')" title="Pouce en bas">👎</span>
                                <span class="emoji" onclick="insertEmoji('😍')" title="Yeux cœur">😍</span>
                                <span class="emoji" onclick="insertEmoji('😢')" title="Triste">😢</span>
                                <span class="emoji" onclick="insertEmoji('😮')" title="Surpris">😮</span>
                                <span class="emoji" onclick="insertEmoji('😡')" title="Colère">😡</span>
                                <span class="emoji" onclick="insertEmoji('🎉')" title="Fête">🎉</span>
                                <span class="emoji" onclick="insertEmoji('🔥')" title="Feu">🔥</span>
                                <span class="emoji" onclick="insertEmoji('💯')" title="100">💯</span>
                                <span class="emoji" onclick="insertEmoji('🚀')" title="Fusée">🚀</span>
                                <span class="emoji" onclick="insertEmoji('💪')" title="Force">💪</span>
                                <span class="emoji" onclick="insertEmoji('🤝')" title="Poignée de main">🤝</span>
                                <span class="emoji" onclick="insertEmoji('✨')" title="Étoiles">✨</span>
                                <span class="emoji" onclick="insertEmoji('🎯')" title="Cible">🎯</span>
                                <span class="emoji" onclick="insertEmoji('💡')" title="Ampoule">💡</span>
                            </div>
                        </div>

                        <!-- Zone de saisie améliorée -->
                        <div class="message-input-wrapper">
                            <div class="input-container">
                                <div class="input-actions-left">
                                    <button type="button" class="action-btn emoji-btn" onclick="toggleEmojiPicker()" title="Émojis">
                                        <i class="fas fa-smile"></i>
                                    </button>
                                    <label for="fileInput" class="action-btn file-btn" title="Pièce jointe">
                                        <i class="fas fa-paperclip"></i>
                                        <input type="file" id="fileInput" style="display: none;"
                                               accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.zip,.rar"
                                               onchange="handleFileSelect(this)" />
                                    </label>
                                </div>

                                <div class="textarea-container">
                                    <textarea rows="1" runat="server" id="txtMessage"
                                             class="message-textarea"
                                             placeholder="Tapez votre message..."
                                             onkeydown="handleKeyPress(event)"
                                             oninput="autoResize(this)"></textarea>
                                </div>

                                <div class="input-actions-right">
                                    <button type="button" runat="server" id="btnenvoie"
                                           class="send-button"
                                           onserverclick="btnenvoie_ServerClick"
                                           title="Envoyer (Ctrl+Entrée)">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                </div>
                            </div>
                            <asp:HiddenField ID="hdnAttachmentUrl" runat="server" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <!-- Modal de création de groupe -->
    <div class="modal fade" id="createGroupModal" tabindex="-1" role="dialog" aria-labelledby="createGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="createGroupModalLabel">
                        <i class="fas fa-users"></i> Créer un Nouveau Groupe
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="txtNomGroupe"><i class="fas fa-tag"></i> Nom du Groupe *</label>
                        <asp:TextBox ID="txtNomGroupe" runat="server" CssClass="form-control"
                            placeholder="Entrez le nom du groupe (ex: Équipe Marketing)"></asp:TextBox>
                        <small class="form-text text-muted">Le nom sera visible par tous les membres</small>
                    </div>

                    <div class="form-group">
                        <label for="txtDescriptionGroupe"><i class="fas fa-info-circle"></i> Description</label>
                        <asp:TextBox ID="txtDescriptionGroupe" runat="server" CssClass="form-control"
                            TextMode="MultiLine" Rows="3"
                            placeholder="Description du groupe (optionnel)"></asp:TextBox>
                    </div>

                    <div class="form-group">
                        <label><i class="fas fa-user-friends"></i> Sélectionner les Membres</label>
                        <div class="members-selection-container">
                            <div class="search-members">
                                <input type="text" id="searchMembers" class="form-control"
                                       placeholder="🔍 Rechercher des membres..."
                                       onkeyup="filterMembers(this.value)">
                            </div>
                            <div class="members-list-container">
                                <asp:CheckBoxList ID="cblMembres" runat="server" CssClass="members-checkbox-list">
                                </asp:CheckBoxList>
                            </div>
                        </div>
                    </div>

                    <div class="selected-members-preview" id="selectedMembersPreview" style="display: none;">
                        <h6><i class="fas fa-check-circle text-success"></i> Membres sélectionnés :</h6>
                        <div id="selectedMembersList" class="selected-members-tags"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                    <asp:Button ID="btnCreerGroupe" runat="server" Text="Créer le Groupe"
                        CssClass="btn btn-primary" OnClick="btnCreerGroupe_Click">
                        <i class="fas fa-plus"></i>
                    </asp:Button>
                </div>
            </div>
        </div>
    </div>

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .chat-wrapper {
            display: flex;
            height: 85vh;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            background: #fff;
            margin: 20px auto;
            max-width: 1200px;
        }

        /* Design moderne de la recherche */
        .search-container {
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            margin-bottom: 15px;
        }

        .search-box {
            position: relative;
            display: flex;
            align-items: center;
            background: white;
            border-radius: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .search-icon {
            position: absolute;
            left: 15px;
            color: #667eea;
            z-index: 2;
        }

        .search-input-modern {
            flex: 1;
            padding: 12px 15px 12px 45px;
            border: none;
            outline: none;
            font-size: 14px;
            background: transparent;
        }

        .search-input-modern::placeholder {
            color: #adb5bd;
        }

        .search-btn-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn-modern:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .quick-actions-modern {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .new-group-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 10px 15px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .new-group-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .refresh-btn {
            padding: 10px 12px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #5a6268;
            transform: rotate(180deg);
        }

        .contacts-panel {
            width: 280px;
            background: #f4f4f4;
            border-right: 1px solid #ddd;
            overflow-y: auto;
        }

        .contacts-header {
            background: #008374;
            color: #fff;
            padding: 15px;
            font-weight: bold;
        }

        .contact-item {
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

            .contact-item:hover {
                background: #e0f7f5;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                object-fit: cover;
            }

        .contact-name {
            font-weight: 500;
        }

        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fafafa;
        }

        .chat-header {
            background: #fff;
            padding: 15px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
        }

        .chat-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .bubble {
            max-width: 60%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            position: relative;
        }

            .bubble.received {
                background: #eee;
                align-self: flex-start;
            }

            .bubble.sent {
                background: #008374;
                color: white;
                align-self: flex-end;
            }

        .chat-footer {
            padding: 12px 15px;
            background: #fff;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

            .chat-footer textarea {
                flex: 1;
                border-radius: 10px;
                padding: 10px;
                border: 1px solid #ccc;
                resize: none;
            }

            .chat-footer button {
                background: #008374;
                color: #fff;
                border: none;
                padding: 10px 20px;
                border-radius: 10px;
                font-weight: bold;
            }

        .online-dot {
            height: 10px;
            width: 10px;
            background-color: #28a745;
            border-radius: 50%;
            display: inline-block;
        }

        .contacts-search {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }

            .contacts-search input {
                width: 100%;
                padding: 8px;
                border-radius: 8px;
                border: 1px solid #ccc;
            }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            position: relative;
        }

            .contact-item:hover {
                background-color: #e2f3f1;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 10px;
            }

        .notification-badge {
            position: absolute;
            right: 10px;
            top: 15px;
            background-color: #ff4b4b;
            color: white;
            padding: 2px 6px;
            font-size: 11px;
            border-radius: 10px;
        }
        .message-container {
    margin-bottom: 15px;
    max-width: 75%;
    padding: 10px;
    border-radius: 10px;
    background-color: #f1f1f1;
}

.sent {
    background-color: #d1f5e0;
    align-self: flex-end;
    margin-left: auto;
}

.received {
    background-color: #fff;
    border: 1px solid #ddd;
    margin-right: auto;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.message-header .avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.message-body p {
    margin: 0;
    font-size: 14px;
}

.attachment-link {
    display: inline-block;
    margin-top: 5px;
    color: #008374;
    font-weight: bold;
}

        /* Styles modernes pour les messages */
        .message-bubble {
            margin: 15px 0;
            display: flex;
            animation: slideInMessage 0.3s ease-out;
        }

        .message-sent {
            justify-content: flex-end;
        }

        .message-received {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            position: relative;
        }

        .message-sent .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 5px 20px;
            margin-left: 50px;
        }

        .message-received .message-content {
            background: #f8f9fa;
            color: #333;
            border-radius: 20px 20px 20px 5px;
            margin-right: 50px;
            border: 1px solid #e9ecef;
        }

        .message-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 15px 5px 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .user-details {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-weight: 600;
            font-size: 13px;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.8;
        }

        .message-sent .user-name,
        .message-sent .message-time {
            color: rgba(255,255,255,0.9);
        }

        .message-received .user-name {
            color: #495057;
        }

        .message-received .message-time {
            color: #6c757d;
        }

        .message-text {
            padding: 5px 15px 10px 15px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .message-footer {
            padding: 0 15px 10px 15px;
            text-align: right;
        }

        .message-date {
            font-size: 10px;
            opacity: 0.7;
        }

        .message-check {
            color: #28a745;
            font-size: 12px;
        }

        /* Styles pour les pièces jointes */
        .file-preview {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .preview-item {
            display: flex;
            align-items: center;
            gap: 12px;
            background: white;
            padding: 10px;
            border-radius: 8px;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }

        .file-info i {
            color: #667eea;
            font-size: 16px;
        }

        .remove-file {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .remove-file:hover {
            background: #c82333;
            transform: scale(1.1);
        }

        .attachment-container {
            margin: 10px 15px;
        }

        .attachment-preview {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255,255,255,0.1);
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .message-received .attachment-preview {
            background: #e9ecef;
            border-color: #dee2e6;
        }

        .attachment-icon {
            color: #667eea;
            font-size: 16px;
        }

        .attachment-details {
            flex: 1;
        }

        .attachment-name {
            font-size: 13px;
            font-weight: 500;
        }

        .attachment-meta {
            font-size: 11px;
            opacity: 0.8;
        }

        .attachment-download {
            color: #28a745;
            text-decoration: none;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .attachment-download:hover {
            background: rgba(40, 167, 69, 0.1);
        }

        /* Zone de saisie moderne */
        .message-input-wrapper {
            background: white;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 8px;
        }

        .input-container {
            display: flex;
            align-items: flex-end;
            gap: 8px;
        }

        .input-actions-left {
            display: flex;
            gap: 5px;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 50%;
            background: #f8f9fa;
            color: #667eea;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
        }

        .action-btn:hover {
            background: #e9ecef;
            transform: scale(1.1);
        }

        .emoji-btn:hover {
            color: #ffc107;
        }

        .file-btn:hover {
            color: #28a745;
        }

        .textarea-container {
            flex: 1;
            position: relative;
        }

        .message-textarea {
            width: 100%;
            min-height: 36px;
            max-height: 120px;
            padding: 8px 15px;
            border: none;
            outline: none;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.4;
            background: transparent;
            overflow-y: auto;
        }

        .message-textarea::placeholder {
            color: #adb5bd;
        }

        .input-actions-right {
            display: flex;
        }

        .send-button {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .send-button:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .send-button:active {
            transform: scale(0.95);
        }

        /* Sélecteur d'émojis amélioré */
        .emoji-picker {
            position: absolute;
            bottom: 50px;
            left: 15px;
            background: white;
            border-radius: 15px;
            padding: 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 1000;
            min-width: 300px;
            overflow: hidden;
        }

        .emoji-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 500;
        }

        .close-emoji {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-emoji:hover {
            background: rgba(255,255,255,0.2);
        }

        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
        }

        .emoji {
            font-size: 22px;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            text-align: center;
            transition: all 0.2s ease;
            position: relative;
        }

        .emoji:hover {
            background: #f8f9fa;
            transform: scale(1.3);
        }

        .emoji:hover::after {
            content: attr(title);
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            white-space: nowrap;
            z-index: 1001;
        }

        /* Styles pour le modal de création de groupe */
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            border-radius: 15px 15px 0 0;
            border-bottom: none;
        }

        .members-selection-container {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .search-members {
            padding: 10px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .members-list-container {
            max-height: 250px;
            overflow-y: auto;
            padding: 10px;
        }

        .members-checkbox-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .members-checkbox-list label {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .members-checkbox-list label:hover {
            background: #f8f9fa;
        }

        .members-checkbox-list input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .selected-members-preview {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .selected-members-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .member-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* Animation pour les messages */
        @keyframes slideInMessage {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .new-message {
            animation: slideInMessage 0.3s ease-out;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .chat-wrapper {
                height: 90vh;
                margin: 10px;
            }

            .contacts-panel {
                width: 250px;
            }

            .emoji-picker {
                left: 10px;
                right: 10px;
                width: auto;
            }

            .message-content {
                max-width: 85%;
            }
        }

    </style>

    <script>
        // Fonctions JavaScript pour les nouvelles fonctionnalités
        function toggleEmojiPicker() {
            const picker = document.getElementById('emojiPicker');
            picker.style.display = picker.style.display === 'none' ? 'block' : 'none';
        }

        function insertEmoji(emoji) {
            const textarea = document.getElementById('<%= txtMessage.ClientID %>');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const text = textarea.value;

            textarea.value = text.substring(0, start) + emoji + text.substring(end);
            textarea.selectionStart = textarea.selectionEnd = start + emoji.length;
            textarea.focus();

            // Fermer le sélecteur d'émojis
            document.getElementById('emojiPicker').style.display = 'none';
        }

        function handleFileSelect(input) {
            const file = input.files[0];
            if (file) {
                // Vérifier la taille du fichier (max 10MB)
                if (file.size > 10 * 1024 * 1024) {
                    alert('Le fichier est trop volumineux. Taille maximale : 10MB');
                    input.value = '';
                    return;
                }

                // Afficher la prévisualisation
                const preview = document.getElementById('filePreview');
                const fileName = preview.querySelector('.file-name');

                fileName.textContent = file.name + ' (' + formatFileSize(file.size) + ')';
                preview.style.display = 'block';

                // Simuler l'upload (à implémenter côté serveur)
                uploadFile(file);
            }
        }

        function removeFile() {
            document.getElementById('fileInput').value = '';
            document.getElementById('filePreview').style.display = 'none';
            document.getElementById('<%= hdnAttachmentUrl.ClientID %>').value = '';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);

            // Afficher un indicateur de chargement
            const preview = document.getElementById('filePreview');
            const fileName = preview.querySelector('.file-name');
            fileName.innerHTML = '📤 Upload en cours... ' + file.name;

            // Upload via AJAX
            fetch('FileUploadHandler.ashx', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Succès de l'upload
                    fileName.innerHTML = '✅ ' + data.originalName + ' (' + formatFileSize(data.fileSize) + ')';
                    document.getElementById('<%= hdnAttachmentUrl.ClientID %>').value = data.fileUrl;
                } else {
                    // Erreur d'upload
                    fileName.innerHTML = '❌ Erreur: ' + data.message;
                    setTimeout(() => {
                        removeFile();
                    }, 3000);
                }
            })
            .catch(error => {
                console.error('Erreur upload:', error);
                fileName.innerHTML = '❌ Erreur de connexion';
                setTimeout(() => {
                    removeFile();
                }, 3000);
            });
        }

        function handleKeyPress(event) {
            if (event.ctrlKey && event.key === 'Enter') {
                event.preventDefault();
                document.getElementById('<%= btnenvoie.ClientID %>').click();
            }
        }

        function showCreateGroupModal() {
            $('#createGroupModal').modal('show');
            loadMembersForGroup();
        }

        function loadMembersForGroup() {
            // Cette fonction sera appelée pour charger les membres disponibles
            // Le chargement se fait déjà côté serveur dans Page_Load
        }

        function filterMembers(searchText) {
            const membersList = document.querySelectorAll('.members-checkbox-list label');
            searchText = searchText.toLowerCase();

            membersList.forEach(function(member) {
                const memberName = member.textContent.toLowerCase();
                if (memberName.includes(searchText)) {
                    member.style.display = 'flex';
                } else {
                    member.style.display = 'none';
                }
            });
        }

        function filterContacts(searchText) {
            const contactItems = document.querySelectorAll('.contact-item');
            searchText = searchText.toLowerCase();

            contactItems.forEach(function(contact) {
                const contactName = contact.textContent.toLowerCase();
                if (contactName.includes(searchText)) {
                    contact.style.display = 'flex';
                } else {
                    contact.style.display = 'none';
                }
            });
        }

        function refreshContacts() {
            // Animation de rotation pour le bouton refresh
            const refreshBtn = document.querySelector('.refresh-btn i');
            refreshBtn.style.transform = 'rotate(360deg)';

            setTimeout(() => {
                refreshBtn.style.transform = 'rotate(0deg)';
                // Ici on pourrait recharger la liste des contacts
                location.reload();
            }, 500);
        }

        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // Auto-resize du textarea
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('<%= txtMessage.ClientID %>');
            if (textarea) {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                });
            }
        });
    </script>

</asp:Content>
